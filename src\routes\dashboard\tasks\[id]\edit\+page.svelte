<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';
  import RecurrenceEditor from '$lib/client/RecurrenceEditor.svelte';
  import { slide } from 'svelte/transition';

  export let data: PageData;

  const { task, categories } = data;

  // Initialize form fields with existing task data
  let title = task.title;
  let notes = task.notes || '';
  let priority = task.priority;
  let categoryId = task.categoryId || '';
  let dueDate = '';
  let dueTime = '';
  let hasDueDate = !!task.dueDate;
  let subtasks: string[] = task.subtasks || [''];
  let loading = false;
  let error = '';
  let success = '';

  // New recurrence rule state, initialized with task data
  let recurrenceRule: any = task.recurrenceRule || null;
  let hasRecurrence = !!task.recurrenceRule;

  // Initialize date and time from existing task
  if (task.dueDate) {
    const taskDate = new Date(task.dueDate);
    dueDate = taskDate.toISOString().split('T')[0];
    dueTime = taskDate.toTimeString().slice(0, 5);
  }

  // Ensure subtasks array has at least one empty string for the UI
  if (subtasks.length === 0) {
    subtasks = [''];
  }

  const priorityOptions = [
    { value: 0, label: 'Low', color: '#64748b' },
    { value: 1, label: 'Normal', color: '#059669' },
    { value: 2, label: 'High', color: '#dc2626' }
  ];

  function addSubtask() {
    subtasks = [...subtasks, ''];
  }

  function removeSubtask(index: number) {
    subtasks = subtasks.filter((_, i) => i !== index);
  }

  function handleRecurrenceChange(event: CustomEvent) {
    recurrenceRule = event.detail;
  }

  async function handleSubmit() {
    if (!title.trim()) {
      error = 'Title is required';
      return;
    }

    loading = true;
    error = '';

    try {
      // Combine date and time if both are provided
      let combinedDueDate = null;
      if (hasDueDate && dueDate) {
        if (dueTime) {
          combinedDueDate = new Date(`${dueDate}T${dueTime}`);
        } else {
          combinedDueDate = new Date(`${dueDate}T09:00`); // Default to 9 AM
        }
      }

      // Filter out empty subtasks
      const validSubtasks = subtasks.filter(s => s.trim() !== '');

      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: title.trim(),
          notes: notes.trim() || null,
          priority,
          categoryId: categoryId || null,
          dueDate: combinedDueDate?.toISOString() || null,
          subtasks: validSubtasks.length > 0 ? validSubtasks : null,
          recurrenceRule: hasRecurrence ? recurrenceRule : null,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        // Show brief success message then redirect
        success = 'Task updated successfully!';
        // Small delay to show success message, then redirect
        setTimeout(() => {
          goto(`/dashboard/tasks/${task.id}`, { invalidateAll: true });
        }, 500);
      } else {
        error = result.error || 'Failed to update task';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>Edit {task.title} - Routine Mail</title>
</svelte:head>

<div class="page-header">
  <div class="breadcrumb">
    <a href="/dashboard">Dashboard</a>
    <span>›</span>
    <a href="/dashboard/tasks">Tasks</a>
    <span>›</span>
    <a href="/dashboard/tasks/{task.id}">{task.title}</a>
    <span>›</span>
    <span class="current">Edit</span>
  </div>
  <h1 class="page-title">Edit Task</h1>
  <p class="page-subtitle">Update your task details</p>
</div>

<div class="form-container">
  <form on:submit|preventDefault={handleSubmit} class="task-form">
    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if success}
      <div class="success-message">{success}</div>
    {/if}

    <div class="form-group">
      <label for="title">Title *</label>
      <input
        id="title"
        type="text"
        bind:value={title}
        placeholder="Enter task title"
        required
        disabled={loading}
      />
    </div>

    <div class="form-group">
      <label>Subtasks</label>
      <div class="subtasks-container">
        {#each subtasks as subtask, index}
          <div class="subtask-row">
            <input
              type="text"
              bind:value={subtasks[index]}
              placeholder="Enter subtask"
              disabled={loading}
            />
            {#if subtasks.length > 1}
              <button
                type="button"
                class="remove-subtask-btn"
                on:click={() => removeSubtask(index)}
                disabled={loading}
              >
                ×
              </button>
            {/if}
          </div>
        {/each}
        <button
          type="button"
          class="add-subtask-btn"
          on:click={addSubtask}
          disabled={loading}
        >
          + Add Subtask
        </button>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label>Priority</label>
        <div class="priority-selector">
          {#each priorityOptions as option}
            <button
              type="button"
              class="priority-btn"
              class:selected={priority === option.value}
              style="--priority-color: {option.color}"
              on:click={() => priority = option.value}
              disabled={loading}
            >
              <div class="priority-indicator"></div>
              {option.label}
            </button>
          {/each}
        </div>
      </div>

      <div class="form-group">
        <label>Category</label>
        <div class="category-selector">
          <button
            type="button"
            class="category-btn"
            class:selected={!categoryId}
            on:click={() => categoryId = ''}
            disabled={loading}
          >
            <div class="category-color" style="background: #e5e7eb;"></div>
            No category
          </button>
          {#each categories as category}
            <button
              type="button"
              class="category-btn"
              class:selected={categoryId === category.id}
              on:click={() => categoryId = category.id}
              disabled={loading}
            >
              <div class="category-color" style="background: {category.color};"></div>
              {category.name}
            </button>
          {/each}
        </div>
      </div>
    </div>

    <div class="form-group">
      <div class="due-date-toggle">
        <label class="toggle-label">
          <input
            type="checkbox"
            bind:checked={hasDueDate}
            disabled={loading}
            class="toggle-checkbox"
          />
          <span class="toggle-text">Set Due Date</span>
        </label>
      </div>

      {#if hasDueDate}
        <div class="due-date-inputs" transition:slide>
          <div class="date-input-group">
            <label class="input-label">Date</label>
            <div class="custom-date-input">
              <input
                id="dueDate"
                type="date"
                bind:value={dueDate}
                disabled={loading}
                class="date-input"
              />
              <div class="input-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
              </div>
            </div>
          </div>

          <div class="time-input-group">
            <label class="input-label">Time (optional)</label>
            <div class="custom-time-input">
              <input
                id="dueTime"
                type="time"
                bind:value={dueTime}
                disabled={loading}
                class="time-input"
                placeholder="09:00"
              />
              <div class="input-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12,6 12,12 16,14"></polyline>
                </svg>
              </div>
            </div>
          </div>
        </div>
      {/if}
    </div>

    <div class="form-group">
      <label for="notes">Notes</label>
      <textarea
        id="notes"
        bind:value={notes}
        placeholder="Add any additional notes (optional)"
        rows="4"
        disabled={loading}
      ></textarea>
    </div>

    <div class="form-section advanced">
       <div class="section-header">
         <h3 class="section-title">Advanced Settings</h3>
         <p class="section-subtitle">Set up recurring tasks and automation</p>
       </div>
       <div class="form-group">
         <div class="recurrence-toggle">
           <label class="toggle-label">
             <input
               type="checkbox"
               bind:checked={hasRecurrence}
               disabled={loading}
               class="toggle-checkbox"
             />
             <span class="toggle-text">Make this a recurring task</span>
           </label>
         </div>
         {#if hasRecurrence}
            <div transition:slide>
                <RecurrenceEditor rule={recurrenceRule} on:change={handleRecurrenceChange} />
            </div>
         {/if}
       </div>
     </div>

    <div class="form-actions">
      <button type="button" class="btn-secondary" on:click={() => goto(`/dashboard/tasks/${task.id}`)} disabled={loading}>
        Cancel
      </button>
      <button type="submit" class="btn-primary" disabled={loading}>
        {loading ? 'Updating...' : 'Update Task'}
      </button>
    </div>
  </form>
</div>

<style>
  .page-header {
    margin-bottom: 2rem;
  }

  .breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
  }

  .breadcrumb a {
    color: #4299e1;
    text-decoration: none;
  }

  .breadcrumb a:hover {
    text-decoration: underline;
  }

  .breadcrumb .current {
    color: #1f2937;
    font-weight: 500;
  }

  .page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    color: #718096;
    font-size: 1.125rem;
    margin: 0;
  }

  .form-container {
    max-width: 700px;
    margin: 0 auto;
  }

  .task-form {
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 2rem;
  }

  .form-section {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .form-section.advanced {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.95), rgba(241, 245, 249, 0.9));
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
  }

  .form-section.advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8, #0ea5e9);
    border-radius: 16px 16px 0 0;
  }

  .form-section.advanced .section-header {
    position: relative;
    z-index: 1;
    padding-top: 0.75rem;
  }

  .form-section.advanced .section-title {
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
  }



  .form-section.advanced .section-subtitle {
    color: #6b7280;
    font-weight: 500;
  }

  .section-header {
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.125rem;
  }

  .section-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  input, textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.2s ease;
  }

  input:focus, textarea:focus {
    outline: none;
    border-color: #4299e1;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(66, 153, 225, 0.1);
    transform: translateY(-1px);
  }

  input:disabled, textarea:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }

  textarea {
    resize: vertical;
    min-height: 100px;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
    border-top: 1px solid #e9ecef;
  }

  .btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 1rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .btn-secondary {
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
  }

  .btn-secondary:hover:not(:disabled) {
    background: rgba(237, 242, 247, 0.9);
  }

  .btn-primary:disabled, .btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .success-message {
    background: #f0fff4;
    color: #38a169;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  /* Priority selector styles */
  .priority-selector {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .priority-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    color: #374151;
  }

  .priority-btn:hover:not(:disabled) {
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .priority-btn.selected {
    border-color: var(--priority-color);
    background: rgba(66, 153, 225, 0.05);
    color: var(--priority-color);
  }

  .priority-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--priority-color);
  }

  /* Category selector styles */
  .category-selector {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 200px;
    overflow-y: auto;
  }

  .category-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    font-weight: 500;
    color: #374151;
  }

  .category-btn:hover:not(:disabled) {
    border-color: #d1d5db;
    background: #f9fafb;
  }

  .category-btn.selected {
    border-color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
    color: #2563eb;
  }

  .category-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  /* Due date inputs */
  .due-date-inputs {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    margin-top: 0.75rem;
    padding: 1.25rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.8));
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .date-input-group, .time-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .input-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .custom-date-input, .custom-time-input {
    position: relative;
    display: flex;
    align-items: center;
  }

  .date-input, .time-input {
    width: 100%;
    padding: 0.875rem 2.5rem 0.875rem 0.875rem;
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    font-size: 0.875rem;
    background: white;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #374151;
  }

  .date-input:focus, .time-input:focus {
    outline: none;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
    transform: translateY(-1px) !important;
    background: rgba(255, 255, 255, 1) !important;
  }

  .date-input:hover, .time-input:hover {
    border-color: #9ca3af;
    transform: translateY(-1px);
  }

  .input-icon {
    position: absolute;
    right: 0.875rem;
    color: #6b7280;
    pointer-events: none;
    transition: color 0.2s;
  }

  .custom-date-input:focus-within .input-icon,
  .custom-time-input:focus-within .input-icon {
    color: #3b82f6;
  }

  /* Toggle styles */
  .toggle-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .toggle-checkbox {
    width: 20px;
    height: 20px;
    margin: 0;
    accent-color: #3b82f6;
  }

  .toggle-text {
    color: #374151;
  }

  /* Subtasks styles */
  .subtasks-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .subtask-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .subtask-row input {
    flex: 1;
    padding: 0.75rem;
    border-radius: 10px;
  }

  .remove-subtask-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #e5e7eb;
    background: white;
    color: #6b7280;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    line-height: 1;
    transition: all 0.2s ease;
  }

  .remove-subtask-btn:hover:not(:disabled) {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
    transform: scale(1.05);
  }

  .add-subtask-btn {
    padding: 0.75rem 1.25rem;
    border: 2px dashed #d1d5db;
    background: transparent;
    color: #6b7280;
    border-radius: 12px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .add-subtask-btn:hover:not(:disabled) {
    border-color: #4299e1;
    color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    .task-form {
      padding: 1.5rem;
      margin: 1rem;
      border-radius: 16px;
    }

    .form-row {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .due-date-container {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .form-actions {
      flex-direction: column;
      gap: 1rem;
    }

    .form-actions button {
      width: 100%;
    }

    .page-title {
      font-size: 1.875rem;
    }
  }
</style>
